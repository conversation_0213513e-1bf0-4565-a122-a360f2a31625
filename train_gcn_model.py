import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, global_mean_pool
from torch_geometric.data import Data, DataLoader
from sklearn.metrics import f1_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

# 设置随机种子
torch.manual_seed(40)
np.random.seed(40)

class GCN(torch.nn.Module):
    """
    图卷积神经网络模型
    """
    def __init__(self, num_node_features, hidden_channels, num_classes):
        super(GCN, self).__init__()
        # 第一层图卷积
        self.conv1 = GCNConv(num_node_features, hidden_channels)
        # 第二层图卷积
        self.conv2 = GCNConv(hidden_channels, hidden_channels)
        # 第三层图卷积
        self.conv3 = GCNConv(hidden_channels, hidden_channels)

        # 分类层
        self.lin = nn.Linear(hidden_channels, num_classes)

    def forward(self, x, edge_index, batch):
        # 第一层图卷积 + 激活函数 + Dropout
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=0.3, training=self.training)

        # 第二层图卷积 + 激活函数 + Dropout
        x = self.conv2(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=0.3, training=self.training)

        # 第三层图卷积 + 激活函数 + Dropout
        x = self.conv3(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=0.2, training=self.training)
        
        # 图池化，计算每个图的平均节点特征
        x = global_mean_pool(x, batch)
        
        # 分类层
        x = self.lin(x)
        
        return x

def load_node_features(subject_id, label):
    """
    加载特定被试的节点特征

    参数:
    - subject_id: 被试ID (完整的文件名前缀，如'ROICorrelation_FisherZ_01001')
    - label: 被试标签 (0=HC, 1=MCI)

    返回:
    - node_features: 节点特征矩阵
    """
    # 根据标签确定特征文件路径
    if label == 0:  # HC (健康对照组)
        # 从ROICorrelation_FisherZ_01001转换为ROISignals_01001
        signals_id = subject_id.replace('ROICorrelation_FisherZ_', 'ROISignals_')
        feature_file = os.path.join('data_fmri', 'selected_timeseries', 'HC', f'{signals_id}_selected.txt')
    elif label == 1:  # MCI (轻度认知障碍)
        # 从ROICorrelation_FisherZ_04001转换为ROISignals_04001
        signals_id = subject_id.replace('ROICorrelation_FisherZ_', 'ROISignals_')
        feature_file = os.path.join('data_fmri', 'selected_timeseries', 'MCI', f'{signals_id}_selected.txt')
    else:
        raise ValueError(f"未知的标签类型: {label}")

    # 检查文件是否存在
    if not os.path.exists(feature_file):
        print(f"节点特征文件不存在: {feature_file}")
        return None

    # 加载时间序列文件
    try:
        # 加载时间序列数据 [time_points, n_regions]
        signals = np.loadtxt(feature_file)

        if signals.ndim == 1:
            # 如果是一维数组，转换为二维
            signals = signals.reshape(-1, 1)

        # 计算每个节点的扩展特征 (使用更丰富的时间序列统计特征)

        # 基础统计特征
        mean = np.mean(signals, axis=0).reshape(-1, 1)      # [n_regions, 1]
        std = np.std(signals, axis=0).reshape(-1, 1)        # [n_regions, 1]
        min_val = np.min(signals, axis=0).reshape(-1, 1)    # [n_regions, 1]
        max_val = np.max(signals, axis=0).reshape(-1, 1)    # [n_regions, 1]
        median = np.median(signals, axis=0).reshape(-1, 1)  # [n_regions, 1]

        # 高阶统计特征
        skewness = stats.skew(signals, axis=0).reshape(-1, 1)  # [n_regions, 1]
        kurtosis = stats.kurtosis(signals, axis=0).reshape(-1, 1)  # [n_regions, 1]

        # 变异性特征
        var = np.var(signals, axis=0).reshape(-1, 1)        # [n_regions, 1]
        range_val = (max_val - min_val)                     # [n_regions, 1]
        iqr = np.percentile(signals, 75, axis=0).reshape(-1, 1) - np.percentile(signals, 25, axis=0).reshape(-1, 1)  # [n_regions, 1]

        # 能量和功率特征
        energy = np.sum(signals**2, axis=0).reshape(-1, 1)  # [n_regions, 1]
        rms = np.sqrt(np.mean(signals**2, axis=0)).reshape(-1, 1)  # [n_regions, 1]

        # 时间域特征
        # 零交叉率
        zero_crossings = np.sum(np.diff(np.sign(signals - mean.T), axis=0) != 0, axis=0).reshape(-1, 1)  # [n_regions, 1]

        # 自相关特征 (lag=1)
        autocorr = np.array([np.corrcoef(signals[:-1, i], signals[1:, i])[0, 1] if len(np.unique(signals[:, i])) > 1 else 0
                           for i in range(signals.shape[1])]).reshape(-1, 1)  # [n_regions, 1]

        # 频域特征 (简化版)
        # 主频率成分的能量
        fft_signals = np.fft.fft(signals, axis=0)
        fft_magnitude = np.abs(fft_signals)
        dominant_freq_energy = np.max(fft_magnitude, axis=0).reshape(-1, 1)  # [n_regions, 1]
        spectral_centroid = np.sum(fft_magnitude * np.arange(fft_magnitude.shape[0]).reshape(-1, 1), axis=0) / (np.sum(fft_magnitude, axis=0) + 1e-8)
        spectral_centroid = spectral_centroid.reshape(-1, 1)  # [n_regions, 1]

        # 组合所有特征 [n_regions, 16]
        features = np.hstack([
            mean, std, min_val, max_val, median,           # 基础统计 (5维)
            skewness, kurtosis, var, range_val, iqr,       # 分布特征 (5维)
            energy, rms, zero_crossings,                   # 能量特征 (3维)
            autocorr, dominant_freq_energy, spectral_centroid  # 时频特征 (3维)
        ])

        # 处理可能的NaN值
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)

        # 标准化特征（Z-score标准化）
        from sklearn.preprocessing import StandardScaler
        scaler = StandardScaler()
        features = scaler.fit_transform(features)

        return features

    except Exception as e:
        print(f"读取文件 {feature_file} 时出错: {str(e)}")
        return None

def load_data_from_split(dataset_type):
    """
    从划分好的数据集中加载数据

    参数:
    - dataset_type: 数据集类型 ('train', 'val', 'test')

    返回:
    - graph_data: 包含所有图数据的列表
    - labels: 对应的标签
    - subject_ids: 对应的被试ID
    """
    # 从划分好的数据集中获取被试ID
    split_dir = f'dataset_split/{dataset_type}'
    if not os.path.exists(split_dir):
        raise FileNotFoundError(f"数据集目录不存在: {split_dir}")

    # 获取HC和MCI的被试ID
    hc_smri_dir = os.path.join(split_dir, 'smri', 'HC')
    mci_smri_dir = os.path.join(split_dir, 'smri', 'MCI')

    split_subject_ids = []
    split_labels = []

    # 从HC组提取被试ID
    if os.path.exists(hc_smri_dir):
        for file in os.listdir(hc_smri_dir):
            if file.endswith('.nii') and file.startswith('ROI_smwp'):
                parts = file.split('_')
                if len(parts) >= 2:
                    full_id = parts[1][4:]  # 去掉'smwp'前缀
                    if len(full_id) >= 6 and full_id.startswith('1'):
                        subject_id = full_id[1:]  # 去掉最前面的1
                        split_subject_ids.append(subject_id)
                        split_labels.append(0)  # HC标签为0

    # 从MCI组提取被试ID
    if os.path.exists(mci_smri_dir):
        for file in os.listdir(mci_smri_dir):
            if file.endswith('.nii') and file.startswith('ROI_smwp'):
                parts = file.split('_')
                if len(parts) >= 2:
                    full_id = parts[1][4:]  # 去掉'smwp'前缀
                    if len(full_id) >= 6 and full_id.startswith('1'):
                        subject_id = full_id[1:]  # 去掉最前面的1
                        split_subject_ids.append(subject_id)
                        split_labels.append(1)  # MCI标签为1

    print(f"{dataset_type}集包含 {len(split_subject_ids)} 个被试")
    print(f"HC: {split_labels.count(0)} 个, MCI: {split_labels.count(1)} 个")

    graph_data = []
    labels = []
    subject_ids = []

    # 为每个被试创建简单的图数据（基于功能连接矩阵）
    for i, subject_id in enumerate(split_subject_ids):
        label = split_labels[i]

        # 从功能连接矩阵文件加载图数据
        if label == 0:  # HC
            matrix_file = os.path.join(split_dir, 'fmri_matrices', 'HC', f'ROICorrelation_FisherZ_{subject_id}_submatrix.txt')
        else:  # MCI
            matrix_file = os.path.join(split_dir, 'fmri_matrices', 'MCI', f'ROICorrelation_FisherZ_{subject_id}_submatrix.txt')

        if not os.path.exists(matrix_file):
            print(f"警告: 功能连接矩阵文件不存在: {matrix_file}")
            continue

        # 加载功能连接矩阵
        try:
            correlation_matrix = np.loadtxt(matrix_file)

            # 创建图的边索引（基于相关性阈值）
            threshold = 0.2  # 相关性阈值（降低以增加连接）
            edge_list = []
            edge_weights = []

            num_nodes = correlation_matrix.shape[0]
            for i_node in range(num_nodes):
                for j_node in range(i_node + 1, num_nodes):
                    if abs(correlation_matrix[i_node, j_node]) > threshold:
                        edge_list.append([i_node, j_node])
                        edge_list.append([j_node, i_node])  # 无向图，添加反向边
                        edge_weights.append(abs(correlation_matrix[i_node, j_node]))
                        edge_weights.append(abs(correlation_matrix[i_node, j_node]))

            if len(edge_list) == 0:
                print(f"警告: 被试 {subject_id} 没有满足阈值的边，跳过")
                continue

            edge_index = torch.tensor(edge_list, dtype=torch.long).T  # PyG期望[2, E]形状
            edge_attr = torch.tensor(edge_weights, dtype=torch.float)

        except Exception as e:
            print(f"加载功能连接矩阵 {matrix_file} 时出错: {e}")
            continue

        # 加载节点特征
        subject_id_for_features = f'ROICorrelation_FisherZ_{subject_id}'
        node_features = load_node_features(subject_id_for_features, label)

        if node_features is not None:
            # 确保节点数量匹配
            if node_features.shape[0] != num_nodes:
                print(f"警告: 被试 {subject_id} 的节点数不匹配，特征数: {node_features.shape[0]}，图中节点数: {num_nodes}")
                # 处理不匹配的情况
                if node_features.shape[0] > num_nodes:
                    node_features = node_features[:num_nodes, :]
                else:
                    padding = np.zeros((num_nodes - node_features.shape[0], node_features.shape[1]))
                    node_features = np.vstack([node_features, padding])

            # 转换为PyTorch张量
            x = torch.tensor(node_features, dtype=torch.float)
        else:
            # 如果无法加载节点特征，使用默认特征（节点度）
            print(f"警告: 使用节点度作为被试 {subject_id} 的默认节点特征")
            degrees = torch.zeros(num_nodes, dtype=torch.float)
            for node in edge_index[0]:
                degrees[node] += 1
            x = degrees.view(-1, 1)

        # 创建PyTorch Geometric的Data对象
        data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr, y=torch.tensor([label], dtype=torch.long))

        # 添加到数据列表
        graph_data.append(data)
        labels.append(label)
        subject_ids.append(subject_id)

    return graph_data, np.array(labels), subject_ids

def train_model(model, train_loader, device, class_weights=None, scheduler=None):
    """
    训练模型

    参数:
    - model: GCN模型
    - train_loader: 训练数据加载器
    - device: 设备
    - class_weights: 类别权重张量
    - scheduler: 学习率调度器
    """
    model.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.0005, weight_decay=1e-4)

    # 使用加权交叉熵损失函数
    if class_weights is not None:
        criterion = torch.nn.CrossEntropyLoss(weight=class_weights)
    else:
        criterion = torch.nn.CrossEntropyLoss()

    total_loss = 0
    for data in train_loader:
        data = data.to(device)
        optimizer.zero_grad()
        out = model(data.x, data.edge_index, data.batch)
        loss = criterion(out, data.y)
        loss.backward()
        optimizer.step()
        total_loss += loss.item() * data.num_graphs

    # 更新学习率调度器
    if scheduler is not None:
        scheduler.step()

    return total_loss / len(train_loader.dataset)

def train_model_with_scheduler(model, train_loader, device, optimizer, scheduler, class_weights=None):
    """
    使用学习率调度器训练模型

    参数:
    - model: GCN模型
    - train_loader: 训练数据加载器
    - device: 设备
    - optimizer: 优化器
    - scheduler: 学习率调度器
    - class_weights: 类别权重张量
    """
    model.train()

    # 使用加权交叉熵损失函数
    if class_weights is not None:
        criterion = torch.nn.CrossEntropyLoss(weight=class_weights)
    else:
        criterion = torch.nn.CrossEntropyLoss()

    total_loss = 0
    for data in train_loader:
        data = data.to(device)
        optimizer.zero_grad()
        out = model(data.x, data.edge_index, data.batch)
        loss = criterion(out, data.y)
        loss.backward()
        optimizer.step()
        total_loss += loss.item() * data.num_graphs

    # 更新学习率调度器
    if scheduler is not None:
        scheduler.step()

    return total_loss / len(train_loader.dataset)

def evaluate_model(model, loader, device):
    """
    评估模型
    """
    model.eval()
    correct = 0
    predictions = []
    targets = []

    with torch.no_grad():
        for data in loader:
            data = data.to(device)
            out = model(data.x, data.edge_index, data.batch)
            pred = out.argmax(dim=1)
            correct += int((pred == data.y).sum())
            predictions.extend(pred.cpu().numpy())
            targets.extend(data.y.cpu().numpy())

    accuracy = correct / len(loader.dataset)
    f1 = f1_score(targets, predictions, average='macro')

    return accuracy, f1, predictions, targets

def calculate_class_weights(labels, method='balanced'):
    """
    计算类别权重

    参数:
    - labels: 标签数组
    - method: 权重计算方法 ('balanced', 'inverse', 'sqrt_inverse', 'enhanced_mci', 'aggressive_mci')

    返回:
    - class_weights: 类别权重数组
    """
    unique_labels, counts = np.unique(labels, return_counts=True)
    total_samples = len(labels)
    n_classes = len(unique_labels)

    if method == 'balanced':
        # sklearn的balanced方法: n_samples / (n_classes * np.bincount(y))
        class_weights = total_samples / (n_classes * counts)
    elif method == 'inverse':
        # 简单的逆频率权重
        class_weights = 1.0 / counts
        class_weights = class_weights / np.sum(class_weights) * n_classes
    elif method == 'sqrt_inverse':
        # 平方根逆频率权重（更温和的权重）
        class_weights = 1.0 / np.sqrt(counts)
        class_weights = class_weights / np.sum(class_weights) * n_classes
    elif method == 'enhanced_mci':
        # 增强MCI权重的方法
        # 基于balanced方法，但给MCI额外的权重加成
        base_weights = total_samples / (n_classes * counts)
        class_weights = base_weights.copy()
        # 假设MCI是标签1，给予2倍的权重加成
        if len(class_weights) > 1:
            class_weights[1] *= 2.0  # MCI权重翻倍
    elif method == 'aggressive_mci':
        # 更激进的MCI权重策略
        # 手动设置权重，强烈偏向MCI识别
        if len(counts) == 2:
            # HC权重较小，MCI权重很大
            class_weights = np.array([0.5, 3.0])  # HC:MCI = 0.5:3.0
        else:
            class_weights = total_samples / (n_classes * counts)
    elif method == 'focal_loss_inspired':
        # 受Focal Loss启发的权重策略
        # 给少数类更大的权重，类似于Focal Loss的思想
        base_weights = total_samples / (n_classes * counts)
        # 计算类别比例
        class_ratios = counts / total_samples
        # 权重与类别比例的平方成反比
        class_weights = 1.0 / (class_ratios ** 1.5)
        # 归一化
        class_weights = class_weights / np.sum(class_weights) * n_classes
    else:
        raise ValueError(f"未知的权重计算方法: {method}")

    return class_weights

def train_and_evaluate(weight_method='balanced'):
    """
    使用训练集、验证集、测试集进行模型训练和评估

    参数:
    - weight_method: 类别权重计算方法
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 加载训练集、验证集、测试集
    print("加载训练集...")
    train_data, train_labels, train_subject_ids = load_data_from_split('train')
    print("加载验证集...")
    val_data, val_labels, val_subject_ids = load_data_from_split('val')
    print("加载测试集...")
    test_data, test_labels, test_subject_ids = load_data_from_split('test')

    # 获取节点特征维度
    num_node_features = train_data[0].x.shape[1]
    print(f"节点特征维度: {num_node_features}")

    # 显示数据分布
    print(f"\n数据分布:")
    _, train_counts = np.unique(train_labels, return_counts=True)
    _, val_counts = np.unique(val_labels, return_counts=True)
    _, test_counts = np.unique(test_labels, return_counts=True)

    print(f"训练集: HC={train_counts[0]} ({train_counts[0]/len(train_labels)*100:.1f}%), MCI={train_counts[1]} ({train_counts[1]/len(train_labels)*100:.1f}%)")
    print(f"验证集: HC={val_counts[0]} ({val_counts[0]/len(val_labels)*100:.1f}%), MCI={val_counts[1]} ({val_counts[1]/len(val_labels)*100:.1f}%)")
    print(f"测试集: HC={test_counts[0]} ({test_counts[0]/len(test_labels)*100:.1f}%), MCI={test_counts[1]} ({test_counts[1]/len(test_labels)*100:.1f}%)")
    print(f"类别权重计算方法: {weight_method}")

    # 计算训练集中的类别权重
    class_weights = calculate_class_weights(train_labels, method=weight_method)
    class_weights_tensor = torch.FloatTensor(class_weights).to(device)

    print(f"类别权重: HC={class_weights[0]:.3f}, MCI={class_weights[1]:.3f}")
    print(f"权重比例 (MCI/HC): {class_weights[1]/class_weights[0]:.2f}")

    # 创建数据加载器
    train_loader = DataLoader(train_data, batch_size=16, shuffle=True)
    val_loader = DataLoader(val_data, batch_size=16, shuffle=False)
    test_loader = DataLoader(test_data, batch_size=16, shuffle=False)

    # 初始化模型
    model = GCN(num_node_features=num_node_features, hidden_channels=64, num_classes=2).to(device)

    # 初始化优化器和余弦退火学习率调度器（添加权重衰减和降低学习率）
    optimizer = torch.optim.Adam(model.parameters(), lr=0.0005, weight_decay=1e-4)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=500, eta_min=1e-6)

    # 训练模型
    best_val_acc = 0
    best_val_f1 = 0
    epochs = 1000
    patience = 100  # 早停耐心值
    patience_counter = 0

    print(f"\n开始训练模型...")
    for epoch in range(1, epochs + 1):
        loss = train_model_with_scheduler(model, train_loader, device, optimizer, scheduler, class_weights_tensor)
        train_acc, _, _, _ = evaluate_model(model, train_loader, device)
        val_acc, val_f1, _, _ = evaluate_model(model, val_loader, device)

        if epoch % 10 == 0:
            print(f'Epoch: {epoch:03d}, Loss: {loss:.4f}, Train Acc: {train_acc:.4f}, Val Acc: {val_acc:.4f}, Val F1: {val_f1:.4f}, Patience: {patience_counter}/{patience}')

        # 保存最佳模型（基于F1分数）
        if val_f1 > best_val_f1:
            best_val_f1 = val_f1
            best_val_acc = val_acc
            torch.save(model.state_dict(), 'best_model.pt')
            patience_counter = 0
        else:
            patience_counter += 1

        # 早停检查
        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch} 轮停止训练")
            break

    # 加载最佳模型
    model.load_state_dict(torch.load('best_model.pt'))

    # 在测试集上评估模型
    test_acc, test_f1, test_preds, test_targets = evaluate_model(model, test_loader, device)
    print(f'\n最终测试集结果:')
    print(f'测试集准确率: {test_acc:.4f}')
    print(f'测试集F1分数: {test_f1:.4f}')

    # 记录每个被试的预测结果
    subject_results = []
    for i, subject_id in enumerate(test_subject_ids):
        subject_results.append({
            'subject_id': subject_id,
            'true_label': test_labels[i],
            'predicted_label': test_preds[i]
        })

    # 保存结果
    results_df = pd.DataFrame(subject_results)
    results_df.to_csv('test_predictions.csv', index=False)
    print(f'测试集预测结果已保存到 test_predictions.csv')

    # 混淆矩阵
    cm = confusion_matrix(test_targets, test_preds)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=['HC', 'MCI'],
                yticklabels=['HC', 'MCI'])
    plt.xlabel('Predict labels')
    plt.ylabel('True labels')
    plt.title('Test Set Confusion Matrix')
    plt.savefig('test_confusion_matrix.png')
    plt.close()

    return test_acc, test_f1

def main():
    print("=== 增强节点特征和MCI权重的GCN模型训练 ===")

    # 使用增强的MCI权重方法
    # 可选方法: 'balanced', 'inverse', 'sqrt_inverse', 'enhanced_mci', 'aggressive_mci', 'focal_loss_inspired'
    weight_method = 'balanced'  # 使用平衡权重
    print(f"开始训练（使用{weight_method}类别权重）...")
    print("特征改进:")
    print("- 节点特征从6维扩展到16维")
    print("- 增加了时频域特征、能量特征等")
    print("- 使用训练集/验证集/测试集划分")

    # 训练和评估模型
    test_acc, test_f1 = train_and_evaluate(weight_method=weight_method)
    print(f"\n=== 最终结果 ===")
    print(f"测试集准确率: {test_acc:.4f}")
    print(f"测试集F1分数: {test_f1:.4f}")

if __name__ == "__main__":
    main() 