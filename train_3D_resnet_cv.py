import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from tqdm import tqdm
import nibabel as nib
from torch.utils.data import Dataset, DataLoader
from monai.transforms import (
    Compose, ScaleIntensity, RandRotate, RandFlip,
    RandZoom, Resize, ToTensor
)
from resnet3D_model import resnet3d

class NiftiDataset(Dataset):
    def __init__(self, dataset_split, classes, transform=None):
        """
        从数据集划分中加载数据
        Args:
            dataset_split: 数据集划分类型 ('train', 'val', 'test')
            classes: 类别列表 ['HC', 'MCI']
            transform: 数据变换
        """
        self.data = []
        self.transform = transform

        # 从dataset_split目录中读取数据
        split_dir = f'dataset_split/{dataset_split}/smri'

        # 遍历每个类别目录（HC和MCI）
        for class_idx, class_name in enumerate(classes):
            class_dir = os.path.join(split_dir, class_name)
            if not os.path.isdir(class_dir):
                continue

            # 收集所有NIfTI文件
            for file_name in os.listdir(class_dir):
                if file_name.endswith('.nii') or file_name.endswith('.nii.gz'):
                    self.data.append({
                        'image_path': os.path.join(class_dir, file_name),
                        'label': class_idx
                    })

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        sample = self.data[idx]

        # 加载NIfTI文件
        img = nib.load(sample['image_path'])
        image_data = img.get_fdata(dtype=np.float32)

        # 应用变换
        if self.transform:
            image_data = self.transform(image_data)

        return image_data, sample['label']

def add_channel_dim(x):
    return x[None, ...]

def train_model(train_loader, val_loader, device, model_name="resnet34", pretrained_path=None):
    """
    训练模型
    Args:
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        device: 设备
        model_name: 模型名称 (resnet18, resnet34, resnet50等)
        pretrained_path: 预训练权重文件路径
    """
    # 创建ResNet模型
    net = resnet3d(model_name=model_name, in_channels=1, num_classes=2, init_weights=True)

    # 加载预训练权重
    if pretrained_path and os.path.exists(pretrained_path):
        print(f"Loading pretrained weights from: {pretrained_path}")
        try:
            checkpoint = torch.load(pretrained_path, map_location=device)
            # 如果checkpoint是字典格式，提取state_dict
            if isinstance(checkpoint, dict) and 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            else:
                state_dict = checkpoint

            # 加载权重，允许部分匹配
            net.load_state_dict(state_dict, strict=False)
            print("Pretrained weights loaded successfully!")
        except Exception as e:
            print(f"Warning: Failed to load pretrained weights: {e}")
            print("Continuing with random initialization...")
    elif pretrained_path:
        print(f"Warning: Pretrained weights file not found: {pretrained_path}")
        print("Continuing with random initialization...")

    net.to(device)
    
    # 打印模型信息
    total_params = sum(p.numel() for p in net.parameters())
    trainable_params = sum(p.numel() for p in net.parameters() if p.requires_grad)
    print(f"Model: {model_name.upper()}")
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    # 训练参数
    epochs = 100
    best_acc = 0.0

    # 定义损失函数和优化器
    loss_function = nn.CrossEntropyLoss()
    optimizer = optim.Adam(net.parameters(), lr=0.0001, weight_decay=0.01)

    # 学习率调度器 - 余弦退火调度
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs, eta_min=1e-6)
    
    print(f"\n=== Training {model_name.upper()} ===")

    for epoch in range(epochs):
        # 训练阶段
        net.train()
        running_loss = 0.0
        train_bar = tqdm(train_loader, file=sys.stdout, desc="Train")

        for data in train_bar:
            images, labels = data
            images, labels = images.to(device), labels.to(device)

            optimizer.zero_grad()
            logits = net(images)
            loss = loss_function(logits, labels)
            loss.backward()
            optimizer.step()

            running_loss += loss.item()
            train_bar.set_postfix(loss=f"{loss.item():.3f}")

        # 更新学习率
        scheduler.step()

        # 验证阶段
        net.eval()
        acc = 0.0
        val_num = len(val_loader.dataset)

        with torch.no_grad():
            val_bar = tqdm(val_loader, file=sys.stdout, desc="Val")
            for val_data in val_bar:
                val_images, val_labels = val_data
                val_images, val_labels = val_images.to(device), val_labels.to(device)

                outputs = net(val_images)
                predict_y = torch.max(outputs, dim=1)[1]
                acc += torch.eq(predict_y, val_labels).sum().item()

        val_accurate = acc / val_num
        avg_loss = running_loss / len(train_loader)
        current_lr = optimizer.param_groups[0]['lr']

        print(f'[epoch {epoch+1}] train_loss: {avg_loss:.3f}  '
              f'val_accuracy: {val_accurate:.3f}  lr: {current_lr:.6f}')

        # 保存最佳模型
        if val_accurate > best_acc:
            best_acc = val_accurate
            torch.save(net.state_dict(), f'./3d_{model_name}_best.pth')
    
    return best_acc

def main():
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using {device} device.")
    
    # 选择模型
    print("\nAvailable ResNet models:")
    print("1. ResNet18 (11M parameters)")
    print("2. ResNet34 (21M parameters) - Recommended")
    print("3. ResNet50 (23M parameters)")
    print("4. ResNet101 (42M parameters)")
    
    try:
        choice = input("Choose model (1-4, default=2): ").strip()
        if not choice:
            choice = "2"
    except:
        choice = "2"
    
    model_map = {
        "1": "resnet18",
        "2": "resnet34", 
        "3": "resnet50",
        "4": "resnet101"
    }
    
    model_name = model_map.get(choice, "resnet34")
    print(f"Selected model: {model_name.upper()}")

    # 选择是否使用预训练权重
    print("\nPretrained weights options:")
    print("1. No pretrained weights (random initialization)")
    print("2. Load from specific file")

    try:
        pretrain_choice = input("Choose option (1-2, default=1): ").strip()
        if not pretrain_choice:
            pretrain_choice = "1"
    except:
        pretrain_choice = "1"

    pretrained_path = None
    if pretrain_choice == "2":
        try:
            pretrained_path = input("Enter pretrained weights file path: ").strip()
            if not pretrained_path:
                print("No path provided, using random initialization")
                pretrained_path = None
        except:
            pretrained_path = None

    if pretrained_path:
        print(f"Will use pretrained weights: {pretrained_path}")
    else:
        print("Using random initialization")

    # 定义数据变换
    train_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        RandRotate(range_x=15, range_y=15, range_z=15, prob=0.5),
        RandFlip(prob=0.5, spatial_axis=0),
        RandZoom(min_zoom=0.9, max_zoom=1.1, prob=0.5),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    val_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    classes = ['HC', 'MCI']

    # 创建训练、验证、测试数据集
    train_dataset = NiftiDataset('train', classes, transform=train_transform)
    val_dataset = NiftiDataset('val', classes, transform=val_transform)
    test_dataset = NiftiDataset('test', classes, transform=val_transform)

    print(f"Train samples: {len(train_dataset)}")
    print(f"Val samples: {len(val_dataset)}")
    print(f"Test samples: {len(test_dataset)}")

    # 创建数据加载器
    batch_size = 32  # ResNet可以处理更大的batch size，但考虑到内存限制
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)

    # 训练模型
    best_val_acc = train_model(train_loader, val_loader, device, model_name, pretrained_path)

    print(f"Best validation accuracy: {best_val_acc:.4f}")

    # 在测试集上评估最佳模型
    print("\n=== Testing Best Model ===")

    # 加载最佳模型
    net = resnet3d(model_name=model_name, in_channels=1, num_classes=2, init_weights=True)
    net.load_state_dict(torch.load(f'./3d_{model_name}_best.pth'))
    net.to(device)
    net.eval()

    test_acc = 0.0
    test_num = len(test_loader.dataset)

    with torch.no_grad():
        test_bar = tqdm(test_loader, file=sys.stdout, desc="Test")
        for test_data in test_bar:
            test_images, test_labels = test_data
            test_images, test_labels = test_images.to(device), test_labels.to(device)

            outputs = net(test_images)
            predict_y = torch.max(outputs, dim=1)[1]
            test_acc += torch.eq(predict_y, test_labels).sum().item()

    test_accuracy = test_acc / test_num
    print(f"Test accuracy: {test_accuracy:.4f}")

    # 保存结果
    results = {
        'model_name': model_name,
        'best_val_accuracy': best_val_acc,
        'test_accuracy': test_accuracy,
        'train_samples': len(train_dataset),
        'val_samples': len(val_dataset),
        'test_samples': len(test_dataset)
    }

    with open(f'resnet_{model_name}_results.json', 'w') as f:
        json.dump(results, f, indent=2)

    print(f"Results saved to 'resnet_{model_name}_results.json'")
    print("Training completed!")

if __name__ == '__main__':
    main()
