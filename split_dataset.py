import os
import shutil
import random
import numpy as np
from collections import defaultdict

def create_directories():
    """创建训练、验证、测试集的目录结构"""
    datasets = ['train', 'val', 'test']
    data_types = ['fmri_timeseries', 'fmri_matrices', 'smri']
    labels = ['HC', 'MCI']
    
    for dataset in datasets:
        for data_type in data_types:
            for label in labels:
                dir_path = os.path.join('dataset_split', dataset, data_type, label)
                os.makedirs(dir_path, exist_ok=True)
                print(f"创建目录: {dir_path}")

def extract_subject_ids_from_fmri(directory):
    """从功能数据文件名中提取被试ID"""
    subject_ids = set()
    if os.path.exists(directory):
        files = os.listdir(directory)
        for file in files:
            if file.endswith('_selected.txt'):
                # 从ROISignals_XXXXX_selected.txt中提取XXXXX
                parts = file.split('_')
                if len(parts) >= 2:
                    subject_id = parts[1]
                    subject_ids.add(subject_id)
    return sorted(list(subject_ids))

def extract_subject_ids_from_smri(directory):
    """从结构数据文件名中提取被试ID"""
    subject_ids = set()
    if os.path.exists(directory):
        files = os.listdir(directory)
        for file in files:
            if file.endswith('.nii'):
                # 从ROI_smwpXXXXXX_...中提取XXXXXX
                if file.startswith('ROI_smwp'):
                    parts = file.split('_')
                    if len(parts) >= 2:
                        subject_id = parts[1][4:]  # 去掉'smwp'前缀
                        subject_ids.add(subject_id)
    return sorted(list(subject_ids))

def find_common_subjects():
    """找到同时具有功能和结构数据的被试"""
    # 提取各类数据的被试ID
    hc_fmri_ids = extract_subject_ids_from_fmri('data_fmri/selected_timeseries/HC')
    mci_fmri_ids = extract_subject_ids_from_fmri('data_fmri/selected_timeseries/MCI')
    hc_smri_ids = extract_subject_ids_from_smri('data_smri/HC')
    mci_smri_ids = extract_subject_ids_from_smri('data_smri/MCI')
    
    # 找到同时具有功能和结构数据的被试
    hc_common = list(set(hc_fmri_ids) & set(hc_smri_ids))
    mci_common = list(set(mci_fmri_ids) & set(mci_smri_ids))
    
    print(f"HC组同时具有功能和结构数据的被试数: {len(hc_common)}")
    print(f"MCI组同时具有功能和结构数据的被试数: {len(mci_common)}")
    
    return hc_common, mci_common

def split_subjects(subject_list, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1):
    """按照指定比例划分被试"""
    random.shuffle(subject_list)
    
    n_total = len(subject_list)
    n_train = int(n_total * train_ratio)
    n_val = int(n_total * val_ratio)
    n_test = n_total - n_train - n_val  # 确保所有被试都被分配
    
    train_subjects = subject_list[:n_train]
    val_subjects = subject_list[n_train:n_train + n_val]
    test_subjects = subject_list[n_train + n_val:]
    
    return train_subjects, val_subjects, test_subjects

def copy_fmri_files(subject_id, label, source_dir, target_dir):
    """复制功能数据文件"""
    # 复制时间序列文件
    timeseries_files = [
        f"ROISignals_{subject_id}_selected.txt",
        f"ROISignals_{subject_id}_selected.csv"
    ]
    
    for file in timeseries_files:
        source_path = os.path.join(f'data_fmri/selected_timeseries/{label}', file)
        if os.path.exists(source_path):
            target_path = os.path.join(target_dir, 'fmri_timeseries', label, file)
            shutil.copy2(source_path, target_path)
    
    # 复制矩阵文件
    matrix_files = [
        f"ROICorrelation_FisherZ_{subject_id}_submatrix.txt",
        f"ROICorrelation_FisherZ_{subject_id}_submatrix.csv"
    ]
    
    for file in matrix_files:
        source_path = os.path.join(f'data_fmri/subject_matrices/{label}', file)
        if os.path.exists(source_path):
            target_path = os.path.join(target_dir, 'fmri_matrices', label, file)
            shutil.copy2(source_path, target_path)

def copy_smri_files(subject_id, label, target_dir):
    """复制结构数据文件"""
    # 查找对应的结构数据文件
    smri_dir = f'data_smri/{label}'
    if os.path.exists(smri_dir):
        files = os.listdir(smri_dir)
        for file in files:
            if file.startswith(f'ROI_smwp{subject_id}_') and file.endswith('.nii'):
                source_path = os.path.join(smri_dir, file)
                target_path = os.path.join(target_dir, 'smri', label, file)
                shutil.copy2(source_path, target_path)
                break

def main():
    # 设置随机种子以确保结果可重现
    random.seed(42)
    np.random.seed(42)
    
    print("开始数据集划分...")
    
    # 创建目录结构
    create_directories()
    
    # 找到同时具有功能和结构数据的被试
    hc_subjects, mci_subjects = find_common_subjects()
    
    # 划分HC组被试
    hc_train, hc_val, hc_test = split_subjects(hc_subjects)
    print(f"\nHC组划分结果:")
    print(f"训练集: {len(hc_train)} 被试")
    print(f"验证集: {len(hc_val)} 被试")
    print(f"测试集: {len(hc_test)} 被试")
    
    # 划分MCI组被试
    mci_train, mci_val, mci_test = split_subjects(mci_subjects)
    print(f"\nMCI组划分结果:")
    print(f"训练集: {len(mci_train)} 被试")
    print(f"验证集: {len(mci_val)} 被试")
    print(f"测试集: {len(mci_test)} 被试")
    
    # 复制文件到对应目录
    datasets = {
        'train': {'HC': hc_train, 'MCI': mci_train},
        'val': {'HC': hc_val, 'MCI': mci_val},
        'test': {'HC': hc_test, 'MCI': mci_test}
    }
    
    print("\n开始复制文件...")
    for dataset_name, labels in datasets.items():
        print(f"\n处理{dataset_name}集...")
        target_base_dir = os.path.join('dataset_split', dataset_name)
        
        for label, subjects in labels.items():
            print(f"  复制{label}组数据...")
            for subject_id in subjects:
                # 复制功能数据
                copy_fmri_files(subject_id, label, None, target_base_dir)
                # 复制结构数据
                copy_smri_files(subject_id, label, target_base_dir)
    
    print("\n数据集划分完成！")
    
    # 生成划分信息文件
    with open('dataset_split/split_info.txt', 'w', encoding='utf-8') as f:
        f.write("数据集划分信息\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("HC组划分:\n")
        f.write(f"训练集 ({len(hc_train)}): {', '.join(hc_train)}\n")
        f.write(f"验证集 ({len(hc_val)}): {', '.join(hc_val)}\n")
        f.write(f"测试集 ({len(hc_test)}): {', '.join(hc_test)}\n\n")
        
        f.write("MCI组划分:\n")
        f.write(f"训练集 ({len(mci_train)}): {', '.join(mci_train)}\n")
        f.write(f"验证集 ({len(mci_val)}): {', '.join(mci_val)}\n")
        f.write(f"测试集 ({len(mci_test)}): {', '.join(mci_test)}\n")
    
    print("划分信息已保存到 dataset_split/split_info.txt")

if __name__ == "__main__":
    main()
