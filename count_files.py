import os
import glob

# 统计各个数据文件夹中的文件数量
def count_files_in_directory(directory):
    if os.path.exists(directory):
        files = [f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]
        return len(files)
    return 0

# 统计功能数据
fmri_hc_timeseries = count_files_in_directory('data_fmri/selected_timeseries/HC')
fmri_mci_timeseries = count_files_in_directory('data_fmri/selected_timeseries/MCI')
fmri_hc_matrices = count_files_in_directory('data_fmri/subject_matrices/HC')
fmri_mci_matrices = count_files_in_directory('data_fmri/subject_matrices/MCI')

# 统计结构数据
smri_hc = count_files_in_directory('data_smri/HC')
smri_mci = count_files_in_directory('data_smri/MCI')

print("=== 数据文件统计 ===")
print(f"功能数据 - HC时间序列文件: {fmri_hc_timeseries}")
print(f"功能数据 - MCI时间序列文件: {fmri_mci_timeseries}")
print(f"功能数据 - HC矩阵文件: {fmri_hc_matrices}")
print(f"功能数据 - MCI矩阵文件: {fmri_mci_matrices}")
print(f"结构数据 - HC文件: {smri_hc}")
print(f"结构数据 - MCI文件: {smri_mci}")

# 提取被试ID
def extract_subject_ids_from_fmri(directory):
    """从功能数据文件名中提取被试ID"""
    subject_ids = set()
    if os.path.exists(directory):
        files = os.listdir(directory)
        for file in files:
            if file.endswith('_selected.txt'):
                # 从ROISignals_XXXXX_selected.txt中提取XXXXX
                parts = file.split('_')
                if len(parts) >= 2:
                    subject_id = parts[1]
                    subject_ids.add(subject_id)
    return sorted(list(subject_ids))

def extract_subject_ids_from_smri(directory):
    """从结构数据文件名中提取被试ID"""
    subject_ids = set()
    if os.path.exists(directory):
        files = os.listdir(directory)
        for file in files:
            if file.endswith('.nii'):
                # 从ROI_smwpXXXXXX_...中提取XXXXXX
                if file.startswith('ROI_smwp'):
                    parts = file.split('_')
                    if len(parts) >= 2:
                        subject_id = parts[1][4:]  # 去掉'smwp'前缀
                        subject_ids.add(subject_id)
    return sorted(list(subject_ids))

# 提取被试ID
hc_fmri_ids = extract_subject_ids_from_fmri('data_fmri/selected_timeseries/HC')
mci_fmri_ids = extract_subject_ids_from_fmri('data_fmri/selected_timeseries/MCI')
hc_smri_ids = extract_subject_ids_from_smri('data_smri/HC')
mci_smri_ids = extract_subject_ids_from_smri('data_smri/MCI')

print(f"\n=== 被试ID统计 ===")
print(f"HC功能数据被试数: {len(hc_fmri_ids)}")
print(f"MCI功能数据被试数: {len(mci_fmri_ids)}")
print(f"HC结构数据被试数: {len(hc_smri_ids)}")
print(f"MCI结构数据被试数: {len(mci_smri_ids)}")

print(f"\nHC功能数据被试ID前10个: {hc_fmri_ids[:10]}")
print(f"MCI功能数据被试ID前10个: {mci_fmri_ids[:10]}")
print(f"HC结构数据被试ID前10个: {hc_smri_ids[:10]}")
print(f"MCI结构数据被试ID前10个: {mci_smri_ids[:10]}")
