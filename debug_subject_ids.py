import os

def extract_subject_ids_from_fmri(directory):
    """从功能数据文件名中提取被试ID"""
    subject_ids = set()
    if os.path.exists(directory):
        files = os.listdir(directory)
        for file in files:
            if file.endswith('_selected.txt'):
                print(f"功能数据文件: {file}")
                # 从ROISignals_XXXXX_selected.txt中提取XXXXX
                parts = file.split('_')
                if len(parts) >= 2:
                    subject_id = parts[1]
                    subject_ids.add(subject_id)
                    print(f"  提取的被试ID: {subject_id}")
                if len(subject_ids) >= 5:  # 只显示前5个
                    break
    return sorted(list(subject_ids))

def extract_subject_ids_from_smri(directory):
    """从结构数据文件名中提取被试ID"""
    subject_ids = set()
    if os.path.exists(directory):
        files = os.listdir(directory)
        for file in files:
            if file.endswith('.nii'):
                print(f"结构数据文件: {file}")
                # 从ROI_smwpXXXXXX_...中提取XXXXXX
                if file.startswith('ROI_smwp'):
                    parts = file.split('_')
                    if len(parts) >= 2:
                        subject_id = parts[1][4:]  # 去掉'smwp'前缀
                        subject_ids.add(subject_id)
                        print(f"  提取的被试ID: {subject_id}")
                if len(subject_ids) >= 5:  # 只显示前5个
                    break
    return sorted(list(subject_ids))

print("=== HC组功能数据 ===")
hc_fmri_ids = extract_subject_ids_from_fmri('data_fmri/selected_timeseries/HC')
print(f"HC功能数据被试ID前5个: {hc_fmri_ids[:5]}")

print("\n=== HC组结构数据 ===")
hc_smri_ids = extract_subject_ids_from_smri('data_smri/HC')
print(f"HC结构数据被试ID前5个: {hc_smri_ids[:5]}")

print("\n=== MCI组功能数据 ===")
mci_fmri_ids = extract_subject_ids_from_fmri('data_fmri/selected_timeseries/MCI')
print(f"MCI功能数据被试ID前5个: {mci_fmri_ids[:5]}")

print("\n=== MCI组结构数据 ===")
mci_smri_ids = extract_subject_ids_from_smri('data_smri/MCI')
print(f"MCI结构数据被试ID前5个: {mci_smri_ids[:5]}")

# 检查交集
hc_common = list(set(hc_fmri_ids) & set(hc_smri_ids))
mci_common = list(set(mci_fmri_ids) & set(mci_smri_ids))

print(f"\n=== 交集检查 ===")
print(f"HC组交集: {len(hc_common)} 个")
print(f"MCI组交集: {len(mci_common)} 个")

if len(hc_common) > 0:
    print(f"HC组交集前5个: {hc_common[:5]}")
if len(mci_common) > 0:
    print(f"MCI组交集前5个: {mci_common[:5]}")
